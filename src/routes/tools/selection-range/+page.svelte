<script lang="ts">
  import { onMount } from 'svelte';
  import { trackToolUsage, trackUserInteraction } from '$lib/analytics';
  import SEOHead from '$lib/components/SEOHead.svelte';
  import { pageSEOData, generateToolStructuredData, siteConfig } from '$lib/seo';
  import { trackFunnelStep } from '$lib/conversionTracking';

  let textArea: HTMLTextAreaElement;
  let demoText = `这是一个演示文本，用于测试 Selection Range API 的各种功能。
你可以在这里选择文本，然后使用下面的按钮来操作选择范围。
Selection Range API 是现代浏览器提供的强大文本操作接口。
通过这个工具，你可以学习如何：
1. 获取当前选择的文本
2. 设置选择范围
3. 操作光标位置
4. 替换选中的文本
5. 在指定位置插入文本

试试选择一些文本，然后点击下面的按钮看看效果！`;

  let selectedText = '';
  let selectionStart = 0;
  let selectionEnd = 0;
  let insertText = '插入的文本';
  let replaceText = '替换的文本';
  let gotoPos = 0;
  let operationResult = '';

  // 获取当前选择的文本和位置
  function getSelection() {
    if (textArea) {
      selectedText = textArea.value.substring(textArea.selectionStart, textArea.selectionEnd);
      selectionStart = textArea.selectionStart;
      selectionEnd = textArea.selectionEnd;
      operationResult = `选择了 ${selectedText.length} 个字符`;
      
      trackToolUsage('Selection Range Tool', 'get_selection', {
        text_length: selectedText.length,
        start: selectionStart,
        end: selectionEnd
      });
    }
  }

  // 选择全部文本
  function selectAll() {
    if (textArea) {
      textArea.select();
      getSelection();
      operationResult = '已选择全部文本';
      
      trackToolUsage('Selection Range Tool', 'select_all', {
        total_length: textArea.value.length
      });
    }
  }

  // 设置选择范围
  function setSelection() {
    if (textArea) {
      const start = Math.max(0, Math.min(selectionStart, textArea.value.length));
      const end = Math.max(start, Math.min(selectionEnd, textArea.value.length));
      
      textArea.setSelectionRange(start, end);
      textArea.focus();
      getSelection();
      operationResult = `设置选择范围: ${start} - ${end}`;
      
      trackToolUsage('Selection Range Tool', 'set_selection', {
        start: start,
        end: end
      });
    }
  }

  // 在当前位置插入文本
  function insertAtCursor() {
    if (textArea && insertText) {
      const start = textArea.selectionStart;
      const end = textArea.selectionEnd;
      const value = textArea.value;
      
      textArea.value = value.substring(0, start) + insertText + value.substring(end);
      textArea.setSelectionRange(start + insertText.length, start + insertText.length);
      textArea.focus();
      
      operationResult = `在位置 ${start} 插入了文本`;
      
      trackToolUsage('Selection Range Tool', 'insert_text', {
        position: start,
        text_length: insertText.length
      });
    }
  }

  // 替换选中的文本
  function replaceSelected() {
    if (textArea && replaceText) {
      const start = textArea.selectionStart;
      const end = textArea.selectionEnd;
      const value = textArea.value;
      
      textArea.value = value.substring(0, start) + replaceText + value.substring(end);
      textArea.setSelectionRange(start, start + replaceText.length);
      textArea.focus();
      
      operationResult = `替换了 ${end - start} 个字符`;
      
      trackToolUsage('Selection Range Tool', 'replace_text', {
        original_length: end - start,
        new_length: replaceText.length
      });
    }
  }

  // 跳转到指定位置
  function gotoPosition() {
    if (textArea) {
      const pos = Math.max(0, Math.min(gotoPos, textArea.value.length));
      textArea.setSelectionRange(pos, pos);
      textArea.focus();

      operationResult = `光标移动到位置 ${pos}`;

      trackToolUsage('Selection Range Tool', 'goto_position', {
        position: pos
      });
    }
  }

  // 复制选中文本到剪贴板
  function copySelection() {
    if (selectedText) {
      navigator.clipboard.writeText(selectedText).then(() => {
        operationResult = `已复制 ${selectedText.length} 个字符到剪贴板`;
        
        trackUserInteraction('copy', 'selection_text', 'clipboard');
        trackToolUsage('Selection Range Tool', 'copy_selection', {
          text_length: selectedText.length
        });
      });
    }
  }

  onMount(() => {
    // 监听文本区域的选择变化
    if (textArea) {
      textArea.addEventListener('select', getSelection);
      textArea.addEventListener('click', getSelection);
      textArea.addEventListener('keyup', getSelection);
    }
    
    // 追踪页面访问
    trackToolUsage('Selection Range Tool', 'page_visit', {});
    trackFunnelStep('tool_usage_funnel', 'tool_page_visit', {
      tool_name: 'Selection Range Tool'
    });
  });

  // 生成工具页面的结构化数据
  const selectionRangeToolStructuredData = generateToolStructuredData(
    'Selection Range 操作工具',
    'Selection Range 在线操作工具，支持文本选择、范围操作、光标定位等功能。提供详细的API说明和实现原理，适合前端开发者学习和使用。',
    `${siteConfig.url}/tools/selection-range`
  );
</script>

<!-- SEO 元数据 -->
<SEOHead seo={pageSEOData.selectionRangeTool} structuredData={selectionRangeToolStructuredData} pathname="/tools/selection-range" />

<main class="container">
  <section class="tool-section">
    <h1>Selection Range 操作工具</h1>
    
    <div class="demo-area">
      <label for="demo-text">演示文本区域：</label>
      <textarea 
        id="demo-text"
        bind:this={textArea} 
        bind:value={demoText}
        rows="8"
        class="demo-textarea"
        placeholder="在这里输入或编辑文本..."
      ></textarea>
    </div>

    <div class="info-panel">
      <div class="selection-info">
        <h3>当前选择信息</h3>
        <p><strong>选中文本：</strong> <span class="selected-text">{selectedText || '无'}</span></p>
        <p><strong>起始位置：</strong> {selectionStart}</p>
        <p><strong>结束位置：</strong> {selectionEnd}</p>
        <p><strong>选择长度：</strong> {selectionEnd - selectionStart}</p>
      </div>
      
      <div class="operation-result">
        <h3>操作结果</h3>
        <p>{operationResult || '请进行操作...'}</p>
      </div>
    </div>

    <div class="controls">
      <div class="control-group">
        <h3>基础操作</h3>
        <button on:click={getSelection} class="btn btn-primary">获取选择</button>
        <button on:click={selectAll} class="btn btn-primary">选择全部</button>
        <button on:click={copySelection} class="btn btn-success" disabled={!selectedText}>复制选中</button>
      </div>

      <div class="control-group">
        <h3>范围设置</h3>
        <div class="input-row">
          <label for="selection-start">起始位置：</label>
          <input id="selection-start" type="number" bind:value={selectionStart} min="0" class="number-input" />
        </div>
        <div class="input-row">
          <label for="selection-end">结束位置：</label>
          <input id="selection-end" type="number" bind:value={selectionEnd} min="0" class="number-input" />
        </div>
        <button on:click={setSelection} class="btn btn-primary">设置选择范围</button>
      </div>

      <div class="control-group">
        <h3>文本操作</h3>
        <div class="input-row">
          <label for="insert-text">插入文本：</label>
          <input id="insert-text" type="text" bind:value={insertText} class="text-input" />
          <button on:click={insertAtCursor} class="btn btn-secondary">插入</button>
        </div>
        <div class="input-row">
          <label for="replace-text">替换文本：</label>
          <input id="replace-text" type="text" bind:value={replaceText} class="text-input" />
          <button on:click={replaceSelected} class="btn btn-secondary" disabled={!selectedText}>替换选中</button>
        </div>
      </div>

      <div class="control-group">
        <h3>光标定位</h3>
        <div class="input-row">
          <label for="goto-position">跳转位置：</label>
          <input id="goto-position" type="number" bind:value={gotoPos} min="0" class="number-input" />
          <button on:click={gotoPosition} class="btn btn-primary">跳转</button>
        </div>
      </div>
    </div>
  </section>

  <section class="desc-section">
    <h2>什么是 Selection Range API？</h2>
    <p>
      Selection Range API 是现代浏览器提供的文本选择和操作接口，主要用于处理用户在文本输入框、文本区域或可编辑元素中的文本选择操作。它允许开发者程序化地获取、设置和操作文本选择范围。
    </p>

    <h3>核心概念</h3>
    <ul>
      <li><strong>selectionStart</strong>：选择范围的起始位置（字符索引）</li>
      <li><strong>selectionEnd</strong>：选择范围的结束位置（字符索引）</li>
      <li><strong>selectionDirection</strong>：选择方向（forward、backward、none）</li>
      <li><strong>setSelectionRange()</strong>：设置选择范围的方法</li>
    </ul>

    <h3>主要方法和属性</h3>
    <div class="code-section">
      <h4>1. 获取选择信息</h4>
      <pre><code>{`// 获取当前选择的起始和结束位置
const start = textArea.selectionStart;
const end = textArea.selectionEnd;

// 获取选中的文本
const selectedText = textArea.value.substring(start, end);`}</code></pre>

      <h4>2. 设置选择范围</h4>
      <pre><code>{`// 设置选择范围
textArea.setSelectionRange(start, end);

// 设置选择范围并指定方向
textArea.setSelectionRange(start, end, 'forward');

// 将光标移动到指定位置
textArea.setSelectionRange(position, position);`}</code></pre>

      <h4>3. 文本操作</h4>
      <pre><code>{`// 在光标位置插入文本
function insertText(element, text) {
  const start = element.selectionStart;
  const end = element.selectionEnd;
  const value = element.value;

  element.value = value.substring(0, start) + text + value.substring(end);
  element.setSelectionRange(start + text.length, start + text.length);
}

// 替换选中的文本
function replaceSelection(element, newText) {
  const start = element.selectionStart;
  const end = element.selectionEnd;
  const value = element.value;

  element.value = value.substring(0, start) + newText + value.substring(end);
  element.setSelectionRange(start, start + newText.length);
}`}</code></pre>
    </div>

    <h3>实际应用场景</h3>
    <ul>
      <li><strong>文本编辑器</strong>：实现代码高亮、自动补全、括号匹配等功能</li>
      <li><strong>富文本编辑</strong>：处理格式化文本的选择和编辑</li>
      <li><strong>代码编辑器</strong>：实现语法高亮、代码折叠、智能缩进</li>
      <li><strong>搜索替换</strong>：在文本中查找和替换特定内容</li>
      <li><strong>文本处理工具</strong>：批量处理文本格式、转换等</li>
    </ul>

    <h3>浏览器兼容性</h3>
    <p>
      Selection Range API 在现代浏览器中有良好的支持：
    </p>
    <ul>
      <li>Chrome 1+</li>
      <li>Firefox 1+</li>
      <li>Safari 1.3+</li>
      <li>Edge 12+</li>
      <li>IE 9+（部分支持）</li>
    </ul>

    <h3>注意事项</h3>
    <ul>
      <li>只能在可编辑元素（input、textarea）中使用</li>
      <li>在某些移动设备上可能有限制</li>
      <li>需要元素获得焦点才能正确工作</li>
      <li>位置索引从 0 开始计算</li>
      <li>设置选择范围时要确保索引在有效范围内</li>
    </ul>

    <h3>扩展功能</h3>
    <p>
      结合其他 Web API 可以实现更强大的功能：
    </p>
    <ul>
      <li><strong>Clipboard API</strong>：复制选中文本到剪贴板</li>
      <li><strong>Drag and Drop API</strong>：拖拽文本选择</li>
      <li><strong>Mutation Observer</strong>：监听文本变化</li>
      <li><strong>Intersection Observer</strong>：优化大文本的性能</li>
    </ul>
  </section>
</main>

<style>
  .container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem 1rem 4rem 1rem;
    font-family: system-ui, sans-serif;
    color: #222;
  }

  .tool-section {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 2px 12px 0 #0001;
    padding: 2rem 1.5rem;
    margin-bottom: 2rem;
  }

  h1 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #2563eb;
    text-align: center;
  }

  .demo-area {
    margin-bottom: 2rem;
  }

  .demo-area label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
  }

  .demo-textarea {
    width: 100%;
    min-height: 200px;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.95rem;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s;
  }

  .demo-textarea:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .info-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .selection-info h3,
  .operation-result h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    color: #1e40af;
  }

  .selection-info p,
  .operation-result p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
  }

  .selected-text {
    background: #fef3c7;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: monospace;
    word-break: break-all;
  }

  .controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .control-group {
    padding: 1.5rem;
    background: #f1f5f9;
    border-radius: 0.5rem;
    border: 1px solid #cbd5e1;
  }

  .control-group h3 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    color: #0f172a;
  }

  .input-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
  }

  .input-row label {
    min-width: 80px;
    font-size: 0.9rem;
    color: #374151;
  }

  .number-input,
  .text-input {
    padding: 0.4rem 0.6rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    font-size: 0.9rem;
    flex: 1;
    min-width: 100px;
  }

  .number-input {
    max-width: 100px;
  }

  .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    margin: 0.25rem;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background: #2563eb;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background: #1d4ed8;
  }

  .btn-secondary {
    background: #6b7280;
    color: white;
  }

  .btn-secondary:hover:not(:disabled) {
    background: #4b5563;
  }

  .btn-success {
    background: #10b981;
    color: white;
  }

  .btn-success:hover:not(:disabled) {
    background: #059669;
  }

  .desc-section {
    background: #f8fafc;
    border-radius: 1rem;
    padding: 2rem 1.5rem;
    box-shadow: 0 1px 6px 0 #0001;
  }

  .desc-section h2 {
    font-size: 1.5rem;
    margin: 0 0 1rem 0;
    color: #0f172a;
  }

  .desc-section h3 {
    font-size: 1.2rem;
    margin: 1.5rem 0 0.75rem 0;
    color: #2563eb;
  }

  .desc-section h4 {
    font-size: 1rem;
    margin: 1rem 0 0.5rem 0;
    color: #374151;
  }

  .desc-section p {
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  .desc-section ul {
    margin: 0.5rem 0 1rem 1.5rem;
    padding: 0;
  }

  .desc-section li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
  }

  .code-section {
    margin: 1.5rem 0;
  }

  pre {
    background: #1e293b;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.5rem 0 1rem 0;
    font-size: 0.85rem;
    line-height: 1.4;
  }

  code {
    font-family: 'Courier New', monospace;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .container {
      padding: 1rem 0.5rem 2rem 0.5rem;
    }

    .tool-section,
    .desc-section {
      padding: 1.5rem 1rem;
    }

    .info-panel {
      grid-template-columns: 1fr;
      gap: 1rem;
      padding: 1rem;
    }

    .controls {
      grid-template-columns: 1fr;
    }

    .input-row {
      flex-direction: column;
      align-items: stretch;
    }

    .input-row label {
      min-width: auto;
    }

    h1 {
      font-size: 1.5rem;
    }

    .desc-section h2 {
      font-size: 1.3rem;
    }

    .desc-section h3 {
      font-size: 1.1rem;
    }

    pre {
      font-size: 0.8rem;
      padding: 0.75rem;
    }
  }

  @media (max-width: 480px) {
    .demo-textarea {
      min-height: 150px;
      font-size: 0.9rem;
    }

    .btn {
      padding: 0.4rem 0.8rem;
      font-size: 0.85rem;
    }
  }
</style>
