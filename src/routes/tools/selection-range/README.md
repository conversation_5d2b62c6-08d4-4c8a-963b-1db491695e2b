# Selection Range 操作工具

## 概述

Selection Range 操作工具是一个交互式的在线工具，用于演示和学习浏览器的 Selection Range API。该工具提供了完整的文本选择和操作功能，适合前端开发者学习和测试。

## 功能特性

### 🎯 核心功能
- **文本选择获取**：实时获取当前选择的文本和位置信息
- **选择范围设置**：程序化设置文本选择的起始和结束位置
- **文本插入**：在光标位置插入指定文本
- **文本替换**：替换当前选中的文本
- **光标定位**：将光标移动到指定位置
- **复制功能**：将选中文本复制到剪贴板

### 📱 响应式设计
- 完全响应式布局，支持桌面和移动设备
- 优化的移动端交互体验
- 自适应的控件布局

### 🔍 SEO 友好
- 完整的 meta 标签配置
- 结构化数据支持
- 语义化的 HTML 结构
- 无障碍访问支持

## 技术实现

### 核心 API
使用了以下浏览器原生 API：
- `HTMLTextAreaElement.selectionStart`
- `HTMLTextAreaElement.selectionEnd`
- `HTMLTextAreaElement.setSelectionRange()`
- `Navigator.clipboard.writeText()`

### 代码示例

```javascript
// 获取选择信息
const selectedText = textArea.value.substring(
  textArea.selectionStart, 
  textArea.selectionEnd
);

// 设置选择范围
textArea.setSelectionRange(start, end);

// 在光标位置插入文本
const start = textArea.selectionStart;
const end = textArea.selectionEnd;
const value = textArea.value;

textArea.value = value.substring(0, start) + 
                 insertText + 
                 value.substring(end);
```

## 教育价值

### 学习目标
- 理解 Selection Range API 的工作原理
- 掌握文本操作的基本方法
- 学习响应式 Web 应用开发
- 了解无障碍访问的最佳实践

### 适用人群
- 前端开发初学者
- 想要学习文本操作 API 的开发者
- 需要实现文本编辑功能的项目团队
- Web API 学习者

## 浏览器兼容性

- ✅ Chrome 1+
- ✅ Firefox 1+
- ✅ Safari 1.3+
- ✅ Edge 12+
- ⚠️ IE 9+（部分支持）

## 使用场景

### 实际应用
- **文本编辑器**：代码高亮、自动补全
- **富文本编辑**：格式化文本处理
- **搜索替换**：文本查找和替换功能
- **代码编辑器**：语法高亮、智能缩进
- **文本处理工具**：批量文本操作

### 开发参考
该工具可以作为以下项目的参考实现：
- 在线代码编辑器
- 文档编辑系统
- 聊天应用的文本输入
- 表单文本处理
- 内容管理系统

## 技术栈

- **前端框架**：SvelteKit 5
- **样式**：原生 CSS + 响应式设计
- **类型检查**：TypeScript
- **分析追踪**：自定义分析系统
- **SEO**：结构化数据 + Meta 标签

## 文件结构

```
src/routes/tools/selection-range/
├── +page.svelte          # 主页面组件
└── README.md            # 文档说明
```

## 开发说明

### 本地开发
```bash
npm run dev
```

### 构建部署
```bash
npm run build
```

### 类型检查
```bash
npm run check
```

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个工具：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

本项目采用 MIT 许可证。
